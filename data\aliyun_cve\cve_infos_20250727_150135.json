{"timestamp": "20250727_150135", "count": 30, "infos": [{"cve_id": "CVE-2025-8220", "description": "已发现Engeman Web版本至********存在一个关键漏洞。受影响的是密码恢复页面组件中的未知功能文件/Login/RecoveryPass。操纵参数LanguageCombobox会导致SQL注入攻击。攻击者可以远程发起攻击。该漏洞已被公开披露，可被利用。该供应商已早期收到此披露通知，但未作出任何回应。", "severity": "HIGH", "cvss_score": 7.3, "published_date": "2025-07-27T00:00:00", "modified_date": "2025-07-27T00:00:00", "references": [], "affected_products": ["云安全中心", "WAF", "云防火墙", "RASP"], "cwe_ids": []}, {"cve_id": "CVE-2025-8219", "description": "发现上海凌顶信息技术有限公司的凌顶CRM版本*******存在一个严重漏洞。该漏洞影响了组件中HTTP POST请求处理器的某些未知处理过程。通过操作参数getvaluestring，可能导致SQL注入攻击。攻击可能来自远程。升级到版本*******可以解决此问题。建议升级受影响的组件。供应商解释：“所有SQL注入向量在v8.6.5+版本中已通过参数化查询和输入清理进行了修补。我们强烈建议所有客户升级到当前版本（v*******），其中包括此修复程序和其他安全增强功能。”", "severity": "MEDIUM", "cvss_score": 6.3, "published_date": "2025-07-27T00:00:00", "modified_date": "2025-07-27T00:00:00", "references": [], "affected_products": ["云安全中心", "WAF", "云防火墙", "RASP"], "cwe_ids": []}, {"cve_id": "CVE-2025-54597", "description": "Heimdall在LinuxServer.io平台上的版本低于2.7.3存在跨站脚本攻击（XSS）漏洞，攻击者可利用该漏洞通过q参数进行攻击。", "severity": "HIGH", "cvss_score": 0.0, "published_date": "2025-07-27T00:00:00", "modified_date": "2025-07-27T00:00:00", "references": [], "affected_products": ["云安全中心", "WAF", "云防火墙", "RASP"], "cwe_ids": []}, {"cve_id": "CVE-2025-6241", "description": "LsiAgent.exe, a component of SysTrack from Lakeside Software, attempts to load several DLL files which are not present in the default installation. If a user-writable directory is present in the SYSTEM PATH environment variable, the user can write a malicious DLL to that directory with arbitrary code. This malicious DLL is executed in the context of NT AUTHORITY\\SYSTEM upon service start or restart, due to the Windows default dynamic-link library search order, resulting in local elevation of privileges.", "severity": "MEDIUM", "cvss_score": 0.0, "published_date": "2025-07-27T00:00:00", "modified_date": "2025-07-27T00:00:00", "references": [], "affected_products": ["云安全中心", "WAF", "云防火墙", "RASP"], "cwe_ids": []}, {"cve_id": "CVE-2025-8211", "description": "发现了一个名为Roothub的漏洞，影响版本至2.6。该漏洞已被声明为问题性漏洞。受影响的功能是文件src/main/java/cn/roothub/web/admin/SystemConfigAdminController.java中的编辑功能。该漏洞可能导致跨站脚本攻击。攻击可以远程发起。该漏洞已被公开披露并可能被利用。", "severity": "MEDIUM", "cvss_score": 5.1, "published_date": "2025-07-27T00:00:00", "modified_date": "2025-07-27T00:00:00", "references": ["https://github.com/wandeorfu/test", "https://vuldb.com/?ctiid.317779", "https://vuldb.com/?id.317779", "https://vuldb.com/?submit.622227", "https://vuldb.com/?submit.622347"], "affected_products": ["云安全中心", "WAF", "云防火墙", "RASP"], "cwe_ids": []}, {"cve_id": "CVE-2025-8210", "description": "在Yeelink Yeelight App的Android版本（最高至3.5.4）中发现了一个漏洞，该漏洞已被归类为问题性漏洞。受影响的是组件com.yeelight.cherry中AndroidManifest.xml的一个未知功能。该漏洞导致应用程序组件被不当导出。本地攻击是必需的。该漏洞已被公开披露并可能被利用。已经提前联系过供应商，但供应商没有做出任何回应。", "severity": "MEDIUM", "cvss_score": 5.3, "published_date": "2025-07-27T00:00:00", "modified_date": "2025-07-27T00:00:00", "references": ["https://github.com/KMov-g/androidapps/blob/main/com.yeelight.cherry.md", "https://vuldb.com/?ctiid.317778", "https://vuldb.com/?id.317778", "https://vuldb.com/?submit.615779"], "affected_products": ["云安全中心", "WAF", "云防火墙", "RASP"], "cwe_ids": []}, {"cve_id": "CVE-2025-8207", "description": "针对Android上的Canara ai1 Mobile Banking App 3.6.23版本发现了一个漏洞，并被归类为问题性漏洞。该问题影响组件com.canarabank.mobility的AndroidManifest.xml文件的某些未知处理过程。操纵这一过程会导致安卓应用程序组件被不当导出。要实施此次攻击需要本地访问权限。该漏洞已被公开披露并可能被利用。虽然已早期联系供应商告知此披露情况，但供应商并未作出任何回应。", "severity": "MEDIUM", "cvss_score": 5.3, "published_date": "2025-07-27T00:00:00", "modified_date": "2025-07-27T00:00:00", "references": ["https://github.com/KMov-g/androidapps/blob/main/com.canarabank.mobility.md", "https://vuldb.com/?ctiid.317777", "https://vuldb.com/?id.317777", "https://vuldb.com/?submit.615777"], "affected_products": ["云安全中心", "WAF", "云防火墙", "RASP"], "cwe_ids": []}, {"cve_id": "CVE-2025-8206", "description": "在Comodo Dragon浏览器（版本至134.0.6998.179）中发现了一个被分类为问题性的漏洞。该漏洞影响IP DNS泄漏检测器组件的未知部分。操纵该组件会导致跨站脚本攻击。有可能发起远程攻击。攻击复杂性相当高。利用该漏洞的难度较大。该漏洞已被公开披露，可能会被利用。供应商已提前收到有关此披露的通知，但未作出任何回应。", "severity": "LOW", "cvss_score": 3.1, "published_date": "2025-07-27T00:00:00", "modified_date": "2025-07-27T00:00:00", "references": ["https://news.fmisec.com/comodo-dragon-vulnerability", "https://vuldb.com/?ctiid.317775", "https://vuldb.com/?id.317775"], "affected_products": ["云安全中心", "WAF", "云防火墙", "RASP"], "cwe_ids": []}, {"cve_id": "CVE-2025-8205", "description": "在Comodo Dragon浏览器（版本至134.0.6998.179）中发现了一个被分类为问题性的漏洞。该漏洞影响组件IP DNS Leakage Detector的某些未知功能。操纵会导致敏感信息的明文传输。攻击可能远程发起。攻击的难度较高，已知利用起来较为困难。该漏洞已被公开披露，可能被利用。尽管已早期联系供应商告知此披露情况，但供应商并未作出任何回应。", "severity": "LOW", "cvss_score": 3.7, "published_date": "2025-07-27T00:00:00", "modified_date": "2025-07-27T00:00:00", "references": ["https://news.fmisec.com/comodo-dragon-vulnerability", "https://vuldb.com/?ctiid.317774", "https://vuldb.com/?id.317774"], "affected_products": ["云安全中心", "WAF", "云防火墙", "RASP"], "cwe_ids": []}, {"cve_id": "CVE-2025-8204", "description": "针对Comodo Dragon版本至134.0.6998.179发现了一个分类为问题的漏洞。该漏洞影响组件HSTS Handler的一个未知功能。操纵会导致对标准的安全检查。攻击可以远程发起。攻击的难度较高。利用该漏洞似乎较为困难。该漏洞已被公开披露，可能会被利用。尽管已提前联系供应商告知此披露信息，但供应商并未作出任何回应。", "severity": "LOW", "cvss_score": 3.1, "published_date": "2025-07-27T00:00:00", "modified_date": "2025-07-27T00:00:00", "references": ["https://news.fmisec.com/comodo-dragon-vulnerability", "https://vuldb.com/?ctiid.317773", "https://vuldb.com/?id.317773", "https://vuldb.com/?submit.615647"], "affected_products": ["云安全中心", "WAF", "云防火墙", "RASP"], "cwe_ids": []}, {"cve_id": "CVE-2025-8203", "description": "已发现 Jing<PERSON> Zeyou 大文件上传控件 6.3 版本中存在一个关键漏洞。受影响的是 /index.jsp 文件的一个未知功能。操纵参数 ID 会导致 SQL 注入。攻击者可远程发起攻击。该漏洞已被公开且可能被利用。该漏洞披露之初已联系供应商，但供应商未作出任何回应。", "severity": "MEDIUM", "cvss_score": 6.3, "published_date": "2025-07-26T00:00:00", "modified_date": "2025-07-26T00:00:00", "references": ["https://github.com/<PERSON>-xin/CVEs/issues/4", "https://vuldb.com/?ctiid.317772", "https://vuldb.com/?id.317772", "https://vuldb.com/?submit.614507"], "affected_products": ["云安全中心", "WAF", "云防火墙", "RASP"], "cwe_ids": []}, {"cve_id": "CVE-2025-8191", "description": "在宏正商城（macrozheng mall）的Swagger UI组件中，存在一个分类为问题的漏洞，影响版本至1.0.3。受影响的是Swagger UI的/swagger-ui/index.html中的一个未知功能。通过操纵configUrl参数，可能导致跨站脚本攻击（Cross Site Scripting）。攻击者可以远程发起攻击。该漏洞已被公开披露并可能被利用。供应商删除了GitHub上关于此漏洞的问题描述，没有给出任何解释。早期通过电子邮件联系供应商进行此披露，但供应商并未作出任何回应。", "severity": "LOW", "cvss_score": 3.5, "published_date": "2025-07-26T00:00:00", "modified_date": "2025-07-26T00:00:00", "references": ["https://github.com/macrozheng/mall/issues/919", "https://github.com/zast-ai/vulnerability-reports/blob/main/mall/DOM_XSS.md", "https://vuldb.com/?ctiid.317604", "https://vuldb.com/?id.317604", "https://vuldb.com/?submit.615731"], "affected_products": ["云安全中心", "WAF", "云防火墙", "RASP"], "cwe_ids": []}, {"cve_id": "CVE-2025-8190", "description": "发现了一个被分类为关键的漏洞，存在于Campcodes Courier Management System 1.0中。这个漏洞影响了某些未知的文件处理过程，具体为 /print_pdets.php 文件。操纵参数ids会导致SQL注入。攻击可能远程发起。该漏洞已被公开披露，可能被利用。", "severity": "MEDIUM", "cvss_score": 6.3, "published_date": "2025-07-26T00:00:00", "modified_date": "2025-07-26T00:00:00", "references": ["https://github.com/XiaoJiesecqwq/CVE/issues/11", "https://vuldb.com/?ctiid.317603", "https://vuldb.com/?id.317603", "https://vuldb.com/?submit.622313", "https://www.campcodes.com/"], "affected_products": ["云安全中心", "WAF", "云防火墙", "RASP"], "cwe_ids": []}, {"cve_id": "CVE-2025-8189", "description": "在Campcodes Courier Management System 1.0中发现了一个被分类为关键的漏洞。该漏洞影响/edit_user.php文件中未知的代码部分。通过操纵参数ID可以导致SQL注入。攻击可以远程发起。该漏洞已被公开披露，可能被利用。", "severity": "MEDIUM", "cvss_score": 6.3, "published_date": "2025-07-26T00:00:00", "modified_date": "2025-07-26T00:00:00", "references": ["https://github.com/XiaoJiesecqwq/CVE/issues/10", "https://vuldb.com/?ctiid.317602", "https://vuldb.com/?id.317602", "https://vuldb.com/?submit.622296", "https://www.campcodes.com/"], "affected_products": ["云安全中心", "WAF", "云防火墙", "RASP"], "cwe_ids": []}, {"cve_id": "CVE-2025-8188", "description": "在Campcodes Courier Management System 1.0中发现了一个被分类为关键的漏洞。该漏洞影响未知部分的文件/edit_staff.php。通过操纵参数ID会导致SQL注入。可以远程发起攻击。该漏洞已被公开披露并可能被利用。", "severity": "MEDIUM", "cvss_score": 6.3, "published_date": "2025-07-26T00:00:00", "modified_date": "2025-07-26T00:00:00", "references": ["https://github.com/XiaoJiesecqwq/CVE/issues/9", "https://vuldb.com/?ctiid.317601", "https://vuldb.com/?id.317601", "https://vuldb.com/?submit.622292", "https://www.campcodes.com/"], "affected_products": ["云安全中心", "WAF", "云防火墙", "RASP"], "cwe_ids": []}, {"cve_id": "CVE-2025-8187", "description": "Campcodes Courier Management System 1.0存在一个漏洞，被评定为高危漏洞。该漏洞影响了文件/edit_parcel.php中的某些未知功能。通过操纵参数ID，可能导致SQL注入攻击。攻击可能来自远程。该漏洞已被公开披露，可能被利用。", "severity": "MEDIUM", "cvss_score": 6.3, "published_date": "2025-07-26T00:00:00", "modified_date": "2025-07-26T00:00:00", "references": ["https://github.com/XiaoJiesecqwq/CVE/issues/8", "https://vuldb.com/?ctiid.317600", "https://vuldb.com/?id.317600", "https://vuldb.com/?submit.622288", "https://www.campcodes.com/"], "affected_products": ["云安全中心", "WAF", "云防火墙", "RASP"], "cwe_ids": []}, {"cve_id": "CVE-2025-8186", "description": "Campcodes Courier Management System 1.0存在一个被认定为关键的漏洞。该漏洞影响/edit_branch.php的未知功能。操纵参数ID会导致SQL注入。攻击可以远程发起。该漏洞已被公开披露并可被利用。", "severity": "MEDIUM", "cvss_score": 6.3, "published_date": "2025-07-26T00:00:00", "modified_date": "2025-07-26T00:00:00", "references": ["https://github.com/XiaoJiesecqwq/CVE/issues/7", "https://vuldb.com/?ctiid.317599", "https://vuldb.com/?id.317599", "https://vuldb.com/?submit.622280", "https://www.campcodes.com/"], "affected_products": ["云安全中心", "WAF", "云防火墙", "RASP"], "cwe_ids": []}, {"cve_id": "CVE-2025-8185", "description": "在“ABC快递管理系统”的1.0版本中发现了严重的安全漏洞。受影响的是文件中一个未知功能 /getbyid.php。通过操纵参数ID，攻击者可以注入SQL代码。攻击可以远程进行。该漏洞已被公开披露，可能被利用。", "severity": "HIGH", "cvss_score": 7.3, "published_date": "2025-07-26T00:00:00", "modified_date": "2025-07-26T00:00:00", "references": ["https://1000projects.org/", "https://github.com/XiaoJiesecqwq/CVE/issues/1", "https://vuldb.com/?ctiid.317598", "https://vuldb.com/?id.317598", "https://vuldb.com/?submit.622261"], "affected_products": ["云安全中心", "WAF", "云防火墙", "RASP"], "cwe_ids": []}, {"cve_id": "CVE-2025-8184", "description": "D-Link DIR-513版本存在漏洞，最高影响至版本1.10，该漏洞被分类为严重级别。漏洞影响组件HTTP POST请求处理器中的文件/goform/formSetWanL2TPtriggers的function testSetWanL2TPcallback功能。操纵此功能会导致基于堆栈的缓冲区溢出。攻击可能远程发起。该漏洞已被公开披露并可能被利用。此漏洞仅影响制造商不再支持的系列产品。", "severity": "HIGH", "cvss_score": 8.8, "published_date": "2025-07-26T00:00:00", "modified_date": "2025-07-26T00:00:00", "references": ["https://github.com/InfiniteLin/<PERSON>-s-CVEdb/blob/main/DIR-513/formSetWanPPTP.md", "https://vuldb.com/?ctiid.317597", "https://vuldb.com/?id.317597", "https://vuldb.com/?submit.622222", "https://www.dlink.com/"], "affected_products": ["云安全中心", "WAF", "云防火墙", "RASP"], "cwe_ids": []}, {"cve_id": "CVE-2025-8182", "description": "这是一个关于腾达AC18版本（版本号为***********）的漏洞描述。该漏洞被归类为问题性漏洞，影响的是Samba组件中的未知代码文件/etc_ro/smb.conf。这一漏洞导致密码要求较弱，攻击者可远程发起攻击。攻击复杂性较高，但利用漏洞似乎较为困难。该漏洞已被公开披露，可能会被利用。", "severity": "MEDIUM", "cvss_score": 5.6, "published_date": "2025-07-26T00:00:00", "modified_date": "2025-07-26T00:00:00", "references": ["https://vuldb.com/?ctiid.317596", "https://vuldb.com/?id.317596", "https://vuldb.com/?submit.621977", "https://www.notion.so/23a54a1113e7802abfabf1275a555f48", "https://www.tenda.com.cn/"], "affected_products": ["云安全中心", "WAF", "云防火墙", "RASP"], "cwe_ids": []}, {"cve_id": "CVE-2025-6991", "description": "Kallyas主题的WordPress存在本地文件包含漏洞，该漏洞存在于所有版本，包括4.21.0版本中的“TH_LatestPosts4”插件。这使得具有贡献者级别及以上访问权限的认证攻击者能够在服务器上包含和执行任意的.php文件，并在这些文件中执行任何PHP代码。这可以用于绕过访问控制，获取敏感数据，或者在可以上传和包含.php文件的情况下实现代码执行。", "severity": "HIGH", "cvss_score": 7.5, "published_date": "2025-07-26T00:00:00", "modified_date": "2025-07-26T00:00:00", "references": ["https://themeforest.net/item/kallyas-responsive-multipurpose-wordpress-theme/4091658", "https://www.wordfence.com/threat-intel/vulnerabilities/id/de1bcbea-5539-456f-94dc-c70fb7acc455?source=cve"], "affected_products": ["云安全中心", "WAF", "云防火墙", "RASP"], "cwe_ids": []}, {"cve_id": "CVE-2025-6989", "description": "Kallyas主题的WordPress存在任意文件夹删除漏洞，该漏洞存在于所有版本至包括4.21.0的delete_font()函数中文件路径验证不足的问题。这使得具有贡献者级别及以上访问权限的认证攻击者能够删除服务器上的任意文件夹。", "severity": "HIGH", "cvss_score": 8.1, "published_date": "2025-07-26T00:00:00", "modified_date": "2025-07-26T00:00:00", "references": ["https://themeforest.net/item/kallyas-responsive-multipurpose-wordpress-theme/4091658", "https://www.wordfence.com/threat-intel/vulnerabilities/id/9a8a3607-4f2e-44fb-8141-75f7620508d4?source=cve"], "affected_products": ["云安全中心", "WAF", "云防火墙", "RASP"], "cwe_ids": []}, {"cve_id": "CVE-2025-5529", "description": "WordPress的Educenter主题存在存储型跨站脚本漏洞，该漏洞存在于所有版本直至并包括版本1.6.2的Circle Counter Block中，原因是输入清理和输出转义不足。这使得拥有贡献者级别及以上访问权限的认证攻击者能够在页面中注入任意网页脚本，每当用户访问注入页面时，脚本就会执行。", "severity": "MEDIUM", "cvss_score": 6.4, "published_date": "2025-07-26T00:00:00", "modified_date": "2025-07-26T00:00:00", "references": ["https://themes.trac.wordpress.org/browser/educenter/1.6.2/blocks-extends/blocks/circle-counter.php#L46", "https://www.wordfence.com/threat-intel/vulnerabilities/id/6f524163-4d4c-40fc-b58a-311f1f6cac15?source=cve"], "affected_products": ["云安全中心", "WAF", "云防火墙", "RASP"], "cwe_ids": []}, {"cve_id": "CVE-2025-8181", "description": "发现了一个被分类为严重的漏洞，存在于TOTOLINK N600R和X2000R的FTP服务组件中，版本号为*******。该漏洞影响文件vsftpd.conf的未知部分。操纵可能导致最低权限违规。攻击者可以远程发起攻击。", "severity": "HIGH", "cvss_score": 7.2, "published_date": "2025-07-26T00:00:00", "modified_date": "2025-07-26T00:00:00", "references": ["https://vuldb.com/?ctiid.317595", "https://vuldb.com/?id.317595", "https://vuldb.com/?submit.621966", "https://vuldb.com/?submit.621968", "https://www.notion.so/23a54a1113e780c08f3acca6a746d732", "https://www.totolink.net/"], "affected_products": ["云安全中心", "WAF", "云防火墙", "RASP"], "cwe_ids": []}, {"cve_id": "CVE-2025-8180", "description": "发现了一个被分类为关键的漏洞，存在于Tenda CH22 *******版本的/goform/deleteUserName文件中的formdeleteUserName功能。该漏洞在于参数old_account的操作会导致缓冲区溢出。攻击可能来自远程。该漏洞已被公开披露，可能被利用。", "severity": "HIGH", "cvss_score": 8.8, "published_date": "2025-07-26T00:00:00", "modified_date": "2025-07-26T00:00:00", "references": ["https://github.com/zhu-haiqin/cve/issues/1", "https://vuldb.com/?ctiid.317594", "https://vuldb.com/?id.317594", "https://vuldb.com/?submit.621964", "https://www.tenda.com.cn/"], "affected_products": ["云安全中心", "WAF", "云防火墙", "RASP"], "cwe_ids": []}, {"cve_id": "CVE-2025-8097", "description": "WordPress中的WoodMart主题在包括8.2.6版本在内的所有版本中，存在不当输入验证漏洞。这是由于woodmart_update_cart_item函数中对qty参数的验证不足导致的。这使得未经身份验证的攻击者可以通过使用小数来操纵购物车数量，通过设置极小的数量（例如，0.00001），使购物车总额四舍五入为$0.00，从而绕过支付要求，未经授权获取虚拟或可下载产品。", "severity": "MEDIUM", "cvss_score": 5.3, "published_date": "2025-07-26T00:00:00", "modified_date": "2025-07-26T00:00:00", "references": ["https://themeforest.net/item/woodmart-woocommerce-wordpress-theme/20264492", "https://www.wordfence.com/threat-intel/vulnerabilities/id/b030aa28-5310-4f69-8b86-7e0b0bae741b?source=cve"], "affected_products": ["云安全中心", "WAF", "云防火墙", "RASP"], "cwe_ids": []}, {"cve_id": "CVE-2025-7501", "description": "WordPress的Wonder Slider Lite插件存在存储型跨站脚本漏洞，该漏洞存在于所有版本直至并包括版本14.4的图片标题和描述DOM中，原因是输入清理和输出转义不足。这使得拥有贡献者级别及以上访问权限的认证攻击者能够在页面中注入任意网页脚本，每当用户访问注入页面时，这些脚本就会执行。", "severity": "MEDIUM", "cvss_score": 6.4, "published_date": "2025-07-26T00:00:00", "modified_date": "2025-07-26T00:00:00", "references": ["https://plugins.trac.wordpress.org/browser/wonderplugin-slider-lite/trunk/engine/wonderpluginslider.js", "https://plugins.trac.wordpress.org/changeset?sfp_email=&sfph_mail=&reponame=&old=3330038%40wonderplugin-slider-lite&new=3330038%40wonderplugin-slider-lite&sfp_email=&sfph_mail=", "https://www.wordfence.com/threat-intel/vulnerabilities/id/320bc1c7-a874-4dc2-92b0-fb5620872ff9?source=cve"], "affected_products": ["云安全中心", "WAF", "云防火墙", "RASP"], "cwe_ids": []}, {"cve_id": "CVE-2025-6987", "description": "WordPress的高级iFrame插件存在存储型跨站脚本漏洞，该漏洞存在于插件的“advanced_iframe”短代码，包括所有截至到并包括版本2025.5的版本。由于对用户提供的属性进行了输入清理和输出转义的不足，使得具有贡献者级别访问权限及以上的认证攻击者能够在用户访问注入页面时注入任意网页脚本并执行。", "severity": "MEDIUM", "cvss_score": 6.4, "published_date": "2025-07-26T00:00:00", "modified_date": "2025-07-26T00:00:00", "references": ["https://plugins.trac.wordpress.org/browser/advanced-iframe/trunk/advanced-iframe.php#L725", "https://plugins.trac.wordpress.org/browser/advanced-iframe/trunk/includes/advanced-iframe-main-iframe.php#L419", "https://plugins.trac.wordpress.org/browser/advanced-iframe/trunk/includes/advanced-iframe-main-read-config.php", "https://plugins.trac.wordpress.org/changeset/3329909/advanced-iframe/trunk/includes/advanced-iframe-main-read-config.php", "https://www.wordfence.com/threat-intel/vulnerabilities/id/6acb99eb-d61c-4d1f-b399-32db07c7e3e7?source=cve"], "affected_products": ["云安全中心", "WAF", "云防火墙", "RASP"], "cwe_ids": []}, {"cve_id": "CVE-2025-8198", "description": "MinimogWP是一款高度转化的电子商务WordPress主题。在直至并包括3.9.0的所有版本中，该主题存在价格操纵漏洞。这是由于在购物车中更改数量时对数量值检查不足导致的。这使得未经身份验证的攻击者可以将商品添加到购物车中，并将数量调整为小数，从而使价格根据小数金额而发生变化。如果安装了WooCommerce版本9.8.2+，则无法利用此漏洞。", "severity": "HIGH", "cvss_score": 7.5, "published_date": "2025-07-26T00:00:00", "modified_date": "2025-07-26T00:00:00", "references": ["https://changelog.thememove.com/minimog-wp/", "https://www.wordfence.com/threat-intel/vulnerabilities/id/cfea0427-78dc-4151-864a-63b6761fc294?source=cve"], "affected_products": ["云安全中心", "WAF", "云防火墙", "RASP"], "cwe_ids": []}, {"cve_id": "CVE-2025-8179", "description": "PHPGurukul本地服务搜索引擎管理系统2.1中发现了一个被分类为关键的漏洞。该漏洞影响了/admin/changeimage.php文件中的未知功能。通过操作参数editid可以导致SQL注入。攻击可以远程发起。该漏洞已被公开披露，可能被利用。", "severity": "HIGH", "cvss_score": 7.3, "published_date": "2025-07-26T00:00:00", "modified_date": "2025-07-26T00:00:00", "references": ["https://github.com/yuan-max11/mycve/issues/1", "https://phpgurukul.com/", "https://vuldb.com/?ctiid.317593", "https://vuldb.com/?id.317593", "https://vuldb.com/?submit.621933"], "affected_products": ["云安全中心", "WAF", "云防火墙", "RASP"], "cwe_ids": []}]}