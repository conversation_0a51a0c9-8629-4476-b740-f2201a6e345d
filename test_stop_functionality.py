#!/usr/bin/env python3
"""
测试停止功能

验证爬虫的停止机制是否正常工作
"""

import asyncio
import threading
import time
from main import AliyunCVECrawler, CrawlConfig


async def test_crawler_stop():
    """测试爬虫停止功能"""
    print("=" * 50)
    print("🧪 测试爬虫停止功能")
    print("=" * 50)
    
    # 创建配置
    config = CrawlConfig(
        max_pages=10,  # 设置较多页数以便测试停止
        delay_range=(1, 2),  # 较短延迟
        timeout=30
    )
    
    # 创建爬虫实例
    crawler = AliyunCVECrawler(config)
    
    try:
        async with crawler:
            print("🚀 开始爬取测试...")
            
            # 创建爬取任务
            crawl_task = asyncio.create_task(
                crawler.crawl_all(start_page=1, max_pages=5)
            )
            
            # 等待2秒后请求停止
            await asyncio.sleep(2)
            print("⏹️ 请求停止爬虫...")
            crawler.request_stop()
            
            # 等待任务完成或被中断
            try:
                results = await asyncio.wait_for(crawl_task, timeout=10)
                print(f"✅ 爬取完成，获得 {len(results)} 个结果")
            except asyncio.TimeoutError:
                print("⚠️ 爬取超时")
                crawl_task.cancel()
            except Exception as e:
                print(f"❌ 爬取异常: {e}")
            
            # 检查停止状态
            if crawler.stop_requested:
                print("✅ 停止请求已生效")
            else:
                print("❌ 停止请求未生效")
                
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    
    print("🏁 测试完成")


def test_gui_stop_simulation():
    """模拟GUI停止功能测试"""
    print("\n" + "=" * 50)
    print("🖥️ 模拟GUI停止功能测试")
    print("=" * 50)
    
    # 模拟GUI的停止机制
    stop_requested = False
    crawler_instance = None
    
    def simulate_crawler():
        """模拟爬虫运行"""
        nonlocal crawler_instance
        
        async def run_crawler():
            config = CrawlConfig(max_pages=5, delay_range=(1, 2))
            crawler_instance = AliyunCVECrawler(config)
            
            try:
                async with crawler_instance:
                    print("🚀 模拟爬虫开始运行...")
                    
                    # 模拟分页爬取
                    for page in range(1, 6):
                        if crawler_instance.stop_requested:
                            print("⏹️ 检测到停止请求，退出爬取")
                            break
                        
                        print(f"📄 正在爬取第 {page} 页...")
                        await asyncio.sleep(1)  # 模拟爬取时间
                    
                    print("✅ 爬虫运行完成")
                    
            except Exception as e:
                print(f"❌ 爬虫运行异常: {e}")
        
        # 运行异步爬虫
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        loop.run_until_complete(run_crawler())
        loop.close()
    
    def simulate_stop_button():
        """模拟点击停止按钮"""
        time.sleep(2)  # 等待2秒
        print("🖱️ 模拟用户点击停止按钮...")
        
        if crawler_instance:
            crawler_instance.request_stop()
            print("📡 已发送停止信号")
        else:
            print("❌ 爬虫实例不存在")
    
    # 启动爬虫线程
    crawler_thread = threading.Thread(target=simulate_crawler, daemon=True)
    crawler_thread.start()
    
    # 启动停止按钮模拟线程
    stop_thread = threading.Thread(target=simulate_stop_button, daemon=True)
    stop_thread.start()
    
    # 等待线程完成
    crawler_thread.join(timeout=10)
    stop_thread.join(timeout=1)
    
    print("🏁 GUI模拟测试完成")


async def test_quick_stop():
    """测试快速停止"""
    print("\n" + "=" * 50)
    print("⚡ 测试快速停止功能")
    print("=" * 50)
    
    config = CrawlConfig(max_pages=1, delay_range=(0.1, 0.2))
    crawler = AliyunCVECrawler(config)
    
    try:
        async with crawler:
            # 立即请求停止
            crawler.request_stop()
            print("⚡ 立即请求停止")
            
            # 尝试爬取
            results = await crawler.crawl_all(start_page=1, max_pages=1)
            print(f"📊 结果数量: {len(results)}")
            
            if crawler.stop_requested:
                print("✅ 快速停止测试通过")
            else:
                print("❌ 快速停止测试失败")
                
    except Exception as e:
        print(f"❌ 快速停止测试异常: {e}")


def main():
    """主函数"""
    print("🧪 AliyunCVE_Crawler 停止功能测试套件")
    print("=" * 60)
    
    # 测试1: 异步停止功能
    try:
        asyncio.run(test_crawler_stop())
    except Exception as e:
        print(f"❌ 异步停止测试失败: {e}")
    
    # 测试2: GUI停止模拟
    try:
        test_gui_stop_simulation()
    except Exception as e:
        print(f"❌ GUI停止模拟失败: {e}")
    
    # 测试3: 快速停止
    try:
        asyncio.run(test_quick_stop())
    except Exception as e:
        print(f"❌ 快速停止测试失败: {e}")
    
    print("\n" + "=" * 60)
    print("🎯 测试总结:")
    print("- 如果看到 '✅ 停止请求已生效'，说明停止功能正常")
    print("- 如果看到 '⏹️ 检测到停止请求，退出爬取'，说明分页停止正常")
    print("- 如果看到 '✅ 快速停止测试通过'，说明即时停止正常")
    print("=" * 60)


if __name__ == "__main__":
    main()
