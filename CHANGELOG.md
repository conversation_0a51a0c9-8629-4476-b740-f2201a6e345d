# 更新日志

本文件记录了项目的所有重要更改。

格式基于 [Keep a Changelog](https://keepachangelog.com/zh-CN/1.0.0/)，
并且本项目遵循 [语义化版本控制](https://semver.org/lang/zh-CN/)。

## [未发布]

### 新增
- 初始版本发布
- 阿里云CVE数据爬取功能
- 批量爬取支持
- 增量爬取功能
- 异步并发处理
- 数据标准化转换
- 详细的配置选项
- 命令行接口
- 完整的文档

### 功能特性
- 支持爬取阿里云漏洞库CVE列表页面
- 支持爬取CVE详情页面
- 自动提取漏洞描述、解决方案、CVSS评分等信息
- 支持增量更新，只爬取指定日期后的新漏洞
- 内置智能延迟和重试机制
- 支持数据去重和缓存
- 提供详细的爬取统计信息
- 支持多种输出格式

### 技术实现
- 使用Playwright进行浏览器自动化
- 异步编程提高爬取效率
- 支持并发控制避免过载
- 完善的错误处理机制
- 模块化设计便于扩展

## [1.0.0] - 2024-01-XX

### 新增
- 项目初始化
- 基础爬虫框架
- CVE数据模型定义
- 配置管理系统

### 已知问题
- 暂无

### 安全性
- 暂无安全相关更新

---

## 版本说明

### 版本号格式
本项目使用语义化版本控制，版本号格式为：`主版本号.次版本号.修订号`

- **主版本号**：当做了不兼容的API修改
- **次版本号**：当做了向下兼容的功能性新增
- **修订号**：当做了向下兼容的问题修正

### 更新类型说明

- **新增 (Added)**: 新功能
- **变更 (Changed)**: 对现有功能的变更
- **弃用 (Deprecated)**: 即将移除的功能
- **移除 (Removed)**: 已移除的功能
- **修复 (Fixed)**: 任何bug修复
- **安全性 (Security)**: 安全相关的修复

### 发布计划

- **主要版本**: 每年1-2次，包含重大功能更新
- **次要版本**: 每季度1-2次，包含新功能和改进
- **修订版本**: 根据需要发布，主要用于bug修复

### 支持政策

- **当前版本**: 完全支持，包含新功能和bug修复
- **前一个主版本**: 仅提供安全更新和严重bug修复
- **更早版本**: 不再提供支持

### 迁移指南

当发布包含破坏性变更的新版本时，我们会在此提供详细的迁移指南。

---

**注意**: 在1.0.0版本发布之前，API可能会有较大变动，请谨慎在生产环境中使用。
