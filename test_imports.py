#!/usr/bin/env python3
"""
AliyunCVE_Crawler 模块导入测试

检查所有必要的模块是否能正确导入
"""

import sys

def test_basic_imports():
    """测试基本导入"""
    try:
        import asyncio
        import json
        import os
        import sys
        import threading
        import tkinter as tk
        from datetime import datetime, timedelta
        from pathlib import Path
        from tkinter import filedialog, messagebox, ttk
        from typing import Dict, List, Optional
        print("✅ 基本模块导入成功")
        return True
    except ImportError as e:
        print(f"❌ 基本模块导入失败: {e}")
        return False

def test_ttkbootstrap():
    """测试ttkbootstrap导入"""
    try:
        import ttkbootstrap as ttk_bs
        from ttkbootstrap.constants import *
        from ttkbootstrap.dialogs import Messagebox
        from ttkbootstrap.scrolled import ScrolledText
        print("✅ ttkbootstrap导入成功")
        return True
    except ImportError as e:
        print(f"❌ ttkbootstrap导入失败: {e}")
        print("请安装: pip install ttkbootstrap")
        return False

def test_main_module():
    """测试主模块导入"""
    try:
        from main import (
            AliyunCVECrawler,
            CrawlConfig,
            crawl_aliyun_cves,
            crawl_aliyun_cves_incremental,
            CVEInfo
        )
        print("✅ 主模块导入成功")
        return True
    except ImportError as e:
        print(f"❌ 主模块导入失败: {e}")
        return False

def test_gui_module():
    """测试GUI模块导入"""
    try:
        from gui import CVECrawlerGUI
        print("✅ GUI模块导入成功")
        return True
    except ImportError as e:
        print(f"❌ GUI模块导入失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("🔍 模块导入测试")
    print("=" * 50)
    
    tests = [
        ("基本模块", test_basic_imports),
        ("ttkbootstrap", test_ttkbootstrap),
        ("主模块", test_main_module),
        ("GUI模块", test_gui_module)
    ]
    
    results = []
    for name, test_func in tests:
        print(f"\n测试 {name}...")
        result = test_func()
        results.append((name, result))
    
    print("\n" + "=" * 50)
    print("📋 测试结果汇总")
    print("=" * 50)
    
    all_passed = True
    for name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{name}: {status}")
        if not result:
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 所有测试通过！GUI应该可以正常启动。")
        print("运行命令: python gui.py")
    else:
        print("⚠️  存在导入问题，请根据上述提示安装缺失的模块。")
    print("=" * 50)

if __name__ == "__main__":
    main()
